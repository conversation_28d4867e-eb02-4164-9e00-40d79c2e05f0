#!/usr/bin/env python3
"""
Test script to verify the enhanced retry logic for intermittent XML parsing errors
"""
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.services.youtube import fetch_transcript
from api.config import Config

def test_enhanced_retry_logic():
    """Test that the enhanced retry logic properly handles XML parsing errors"""
    
    print("Testing Enhanced Retry Logic for Intermittent XML Parsing Errors")
    print("=" * 70)
    
    # Check proxy configuration
    proxy_dict = Config.get_proxy_dict()
    if proxy_dict:
        print(f"✅ Proxy configured: {Config.DECODO_HOST}:{Config.DECODO_PORT}")
    else:
        print("❌ No proxy configuration found")
        print("   Set DECODO_USERNAME and DECODO_PASSWORD to test proxy retry logic")
        return
    
    # Test with the video from the error logs
    test_video_id = "5PdjAKZcEpg"
    
    print(f"\nTesting enhanced retry for video: {test_video_id}")
    print("This will show how the retry logic handles any intermittent XML parsing errors")
    print("-" * 70)
    
    try:
        transcript = fetch_transcript(test_video_id, timeout_limit=60)
        
        if transcript:
            print(f"\n✅ SUCCESS: Fetched transcript with {len(transcript)} entries")
            print("The enhanced retry logic is working correctly!")
            print("\nFirst few entries:")
            for i, entry in enumerate(transcript[:3]):
                print(f"  {i+1}. [{entry['start']:.1f}s] {entry['text'][:60]}...")
        else:
            print("\n❌ FAILED: No transcript returned")
            print("This could be due to:")
            print("  - Video has no captions")
            print("  - All retry attempts failed")
            print("  - Proxy configuration issues")
            
    except Exception as e:
        print(f"\n❌ ERROR: {type(e).__name__}: {e}")
        import traceback
        traceback.print_exc()

def explain_fix():
    """Explain what the fix does"""
    print("\n" + "=" * 70)
    print("EXPLANATION OF THE FIX")
    print("=" * 70)
    print("""
The original code already had 3 retry attempts for proxy connections, but the 
XML parsing error occurred INSIDE the YouTubeTranscriptApi.get_transcript() call.

BEFORE (the issue):
- 3 retries for connection errors ✅
- XML parsing errors caused immediate failure ❌

AFTER (the fix):
- 3 retries for connection errors ✅  
- 3 retries for XML parsing errors ✅ (NEW)

The fix wraps the transcript API call in a try-catch block within the existing
retry loop, so now XML parsing errors are caught and retried properly.

This should significantly reduce the intermittent failures you were experiencing
with the Decodo proxy.
""")

if __name__ == "__main__":
    test_enhanced_retry_logic()
    explain_fix()
