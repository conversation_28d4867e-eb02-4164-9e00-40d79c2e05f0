# Flask and Flask extensions removed as part of Sanic migration
werkzeug==2.0.3
youtube-transcript-api==1.0.2
fastapi>=0.110.0
uvicorn[standard]>=0.27.0
openai>=1.75.0
python-dotenv==1.0.0
httpx>=0.28.1
httpcore>=1.0.3
google-generativeai==0.8.4
# PyJWT==2.8.0 # Replaced by python-jose
redis==5.0.1 # Standard redis client (Keep in case sync operations are needed elsewhere)
# aioredis # Removed, replaced by upstash-redis
# setuptools # No longer needed for aioredis workaround
upstash-redis>=1.0.0 # Use official Upstash client
passlib>=1.7.4 # For password hashing
bcrypt>=3.2.0 # Required by passlib[bcrypt]
python-jose[cryptography]>=3.3.0 # For JWT handling
email-validator>=2.0.0 # Required by pydantic for EmailStr validation
stripe==7.9.0
google-auth==2.27.0
google-auth-oauthlib==1.2.0
authlib==1.3.0
slowapi>=0.1.8
# Removed aioredis dependency (caused distutils error on Vercel)
